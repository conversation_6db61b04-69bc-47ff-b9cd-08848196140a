# 企业级RAG知识库系统 - 文档解析与预处理模块设计

## 1. 模块概述

文档解析与预处理模块是RAG系统的核心入口，负责将各种格式的文档转换为结构化的文本数据，为后续的向量化和检索提供高质量的数据基础。

### 1.1 核心功能

- **多格式支持**：PDF、Word、Excel、PowerPoint、TXT、Markdown、图片
- **智能解析**：文档结构识别、表格提取、图片OCR
- **元数据提取**：作者、创建时间、文档类型、标题层级
- **文本预处理**：清洗、标准化、语义分块
- **异步处理**：基于RocketMQ的大批量文档处理

### 1.2 技术架构

```mermaid
graph TB
    subgraph "文档解析服务"
        DocController[文档控制器]
        ParseManager[解析管理器]
        FormatDetector[格式检测器]
        
        subgraph "解析器工厂"
            PDFParser[PDF解析器]
            WordParser[Word解析器]
            ExcelParser[Excel解析器]
            PPTParser[PPT解析器]
            TxtParser[文本解析器]
            ImageParser[图片解析器]
        end
        
        subgraph "预处理器"
            TextCleaner[文本清洗器]
            StructureExtractor[结构提取器]
            MetadataExtractor[元数据提取器]
            ChunkProcessor[分块处理器]
        end
    end
    
    subgraph "外部服务"
        OCRService[OCR服务]
        NLPService[NLP服务]
    end
    
    subgraph "存储"
        MinIO[文件存储]
        MySQL[元数据存储]
        RocketMQ[消息队列]
    end
    
    DocController --> ParseManager
    ParseManager --> FormatDetector
    FormatDetector --> PDFParser
    FormatDetector --> WordParser
    FormatDetector --> ExcelParser
    FormatDetector --> PPTParser
    FormatDetector --> TxtParser
    FormatDetector --> ImageParser
    
    PDFParser --> OCRService
    ImageParser --> OCRService
    
    ParseManager --> TextCleaner
    TextCleaner --> StructureExtractor
    StructureExtractor --> MetadataExtractor
    MetadataExtractor --> ChunkProcessor
    
    ChunkProcessor --> NLPService
    ParseManager --> MinIO
    ParseManager --> MySQL
    ParseManager --> RocketMQ
```

## 2. 核心组件设计

### 2.1 文档解析管理器

```java
@Service
@Slf4j
public class DocumentParseManager {
    
    @Autowired
    private ParserFactory parserFactory;
    
    @Autowired
    private TextPreprocessor textPreprocessor;
    
    @Autowired
    private RocketMQTemplate rocketMQTemplate;
    
    /**
     * 解析文档主流程
     */
    public ParseResult parseDocument(DocumentInfo docInfo) {
        try {
            // 1. 格式检测
            DocumentFormat format = detectFormat(docInfo);
            
            // 2. 选择解析器
            DocumentParser parser = parserFactory.getParser(format);
            
            // 3. 执行解析
            RawContent rawContent = parser.parse(docInfo);
            
            // 4. 文本预处理
            ProcessedContent processedContent = textPreprocessor.process(rawContent);
            
            // 5. 发送向量化消息
            sendVectorizationMessage(processedContent);
            
            return ParseResult.success(processedContent);
            
        } catch (Exception e) {
            log.error("文档解析失败: {}", docInfo.getFileName(), e);
            return ParseResult.failure(e.getMessage());
        }
    }
}
```

### 2.2 解析器工厂模式

```java
@Component
public class ParserFactory {
    
    private final Map<DocumentFormat, DocumentParser> parsers;
    
    public ParserFactory(List<DocumentParser> parserList) {
        this.parsers = parserList.stream()
            .collect(Collectors.toMap(
                DocumentParser::getSupportedFormat,
                Function.identity()
            ));
    }
    
    public DocumentParser getParser(DocumentFormat format) {
        DocumentParser parser = parsers.get(format);
        if (parser == null) {
            throw new UnsupportedFormatException("不支持的文档格式: " + format);
        }
        return parser;
    }
}
```

### 2.3 PDF解析器实现

```java
@Component
@Slf4j
public class PDFDocumentParser implements DocumentParser {
    
    @Autowired
    private OCRService ocrService;
    
    @Override
    public DocumentFormat getSupportedFormat() {
        return DocumentFormat.PDF;
    }
    
    @Override
    public RawContent parse(DocumentInfo docInfo) {
        try (PDDocument document = PDDocument.load(docInfo.getInputStream())) {
            
            RawContent.Builder builder = RawContent.builder()
                .documentId(docInfo.getDocumentId())
                .fileName(docInfo.getFileName());
            
            PDFTextStripper textStripper = new PDFTextStripper();
            
            for (int pageNum = 1; pageNum <= document.getNumberOfPages(); pageNum++) {
                // 提取文本
                textStripper.setStartPage(pageNum);
                textStripper.setEndPage(pageNum);
                String pageText = textStripper.getText(document);
                
                // 提取图片并OCR
                List<String> imageTexts = extractAndOCRImages(document, pageNum);
                
                // 构建页面内容
                PageContent pageContent = PageContent.builder()
                    .pageNumber(pageNum)
                    .textContent(pageText)
                    .imageTexts(imageTexts)
                    .build();
                
                builder.addPage(pageContent);
            }
            
            // 提取元数据
            PDDocumentInformation info = document.getDocumentInformation();
            DocumentMetadata metadata = extractMetadata(info);
            builder.metadata(metadata);
            
            return builder.build();
            
        } catch (IOException e) {
            throw new DocumentParseException("PDF解析失败", e);
        }
    }
    
    private List<String> extractAndOCRImages(PDDocument document, int pageNum) {
        // 提取页面图片并调用OCR服务
        List<BufferedImage> images = extractImagesFromPage(document, pageNum);
        return images.stream()
            .map(ocrService::recognizeText)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toList());
    }
}
```

## 3. 文本预处理设计

### 3.1 文本清洗器

```java
@Component
public class TextCleaner {
    
    private static final Pattern NOISE_PATTERN = Pattern.compile(
        "[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]|" +  // 控制字符
        "\\p{Cntrl}|" +                                // 其他控制字符
        "\\p{So}|" +                                   // 其他符号
        "\\p{Cn}"                                      // 未分配字符
    );
    
    public String clean(String text) {
        if (StringUtils.isBlank(text)) {
            return "";
        }
        
        return text
            // 移除噪声字符
            .replaceAll(NOISE_PATTERN.pattern(), "")
            // 标准化空白字符
            .replaceAll("\\s+", " ")
            // 移除多余的换行
            .replaceAll("\\n{3,}", "\n\n")
            // 去除首尾空白
            .trim();
    }
}
```

### 3.2 智能分块处理器

```java
@Component
@Slf4j
public class SemanticChunkProcessor {
    
    @Autowired
    private NLPService nlpService;
    
    private static final int MAX_CHUNK_SIZE = 512;
    private static final int OVERLAP_SIZE = 50;
    
    public List<TextChunk> processChunks(ProcessedContent content) {
        List<TextChunk> chunks = new ArrayList<>();
        
        for (PageContent page : content.getPages()) {
            // 按段落分割
            List<String> paragraphs = splitByParagraphs(page.getTextContent());
            
            // 语义分块
            List<String> semanticChunks = createSemanticChunks(paragraphs);
            
            // 转换为TextChunk对象
            for (int i = 0; i < semanticChunks.size(); i++) {
                TextChunk chunk = TextChunk.builder()
                    .documentId(content.getDocumentId())
                    .pageNumber(page.getPageNumber())
                    .chunkIndex(i)
                    .content(semanticChunks.get(i))
                    .metadata(createChunkMetadata(page, i))
                    .build();
                
                chunks.add(chunk);
            }
        }
        
        return chunks;
    }
    
    private List<String> createSemanticChunks(List<String> paragraphs) {
        List<String> chunks = new ArrayList<>();
        StringBuilder currentChunk = new StringBuilder();
        
        for (String paragraph : paragraphs) {
            // 检查是否需要开始新的分块
            if (shouldStartNewChunk(currentChunk.toString(), paragraph)) {
                if (currentChunk.length() > 0) {
                    chunks.add(currentChunk.toString().trim());
                    
                    // 添加重叠内容
                    currentChunk = new StringBuilder(getOverlapContent(currentChunk.toString()));
                }
            }
            
            currentChunk.append(paragraph).append("\n");
        }
        
        if (currentChunk.length() > 0) {
            chunks.add(currentChunk.toString().trim());
        }
        
        return chunks;
    }
    
    private boolean shouldStartNewChunk(String currentChunk, String newParagraph) {
        // 基于长度的判断
        if (currentChunk.length() + newParagraph.length() > MAX_CHUNK_SIZE) {
            return true;
        }
        
        // 基于语义的判断（调用NLP服务）
        return nlpService.isSemanticBoundary(currentChunk, newParagraph);
    }
}
```

## 4. 异步处理设计

### 4.1 消息生产者

```java
@Component
@Slf4j
public class DocumentParseProducer {
    
    @Autowired
    private RocketMQTemplate rocketMQTemplate;
    
    public void sendParseMessage(DocumentParseMessage message) {
        try {
            rocketMQTemplate.convertAndSend(
                RocketMQTopics.DOCUMENT_PARSE,
                message,
                MessageBuilder.withPayload(message)
                    .setHeader("documentId", message.getDocumentId())
                    .setHeader("priority", message.getPriority())
                    .build()
            );
            
            log.info("发送文档解析消息成功: {}", message.getDocumentId());
            
        } catch (Exception e) {
            log.error("发送文档解析消息失败: {}", message.getDocumentId(), e);
            throw new MessageSendException("消息发送失败", e);
        }
    }
}
```

### 4.2 消息消费者

```java
@Component
@RocketMQMessageListener(
    topic = RocketMQTopics.DOCUMENT_PARSE,
    consumerGroup = "document-parse-consumer",
    consumeMode = ConsumeMode.CONCURRENTLY,
    messageModel = MessageModel.CLUSTERING
)
@Slf4j
public class DocumentParseConsumer implements RocketMQListener<DocumentParseMessage> {
    
    @Autowired
    private DocumentParseManager parseManager;
    
    @Override
    public void onMessage(DocumentParseMessage message) {
        String documentId = message.getDocumentId();
        
        try {
            log.info("开始处理文档解析消息: {}", documentId);
            
            // 更新文档状态为处理中
            updateDocumentStatus(documentId, DocumentStatus.PARSING);
            
            // 执行解析
            ParseResult result = parseManager.parseDocument(message.getDocumentInfo());
            
            if (result.isSuccess()) {
                updateDocumentStatus(documentId, DocumentStatus.PARSE_SUCCESS);
                log.info("文档解析成功: {}", documentId);
            } else {
                updateDocumentStatus(documentId, DocumentStatus.PARSE_FAILED);
                log.error("文档解析失败: {}, 错误: {}", documentId, result.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("处理文档解析消息异常: {}", documentId, e);
            updateDocumentStatus(documentId, DocumentStatus.PARSE_FAILED);
            throw new RuntimeException("文档解析处理失败", e);
        }
    }
}
```

## 5. 支持的文档格式清单

### 5.1 文档格式支持矩阵

| 格式类别 | 文件格式 | 扩展名 | MIME类型 | 解析器 | OCR需求 | 特殊处理 |
|----------|----------|--------|----------|--------|---------|----------|
| **PDF文档** | PDF | .pdf | application/pdf | PDFParser | 图片页面 | 密码保护、数字签名 |
| **Office文档** | Word | .docx,.doc | application/vnd.openxmlformats-officedocument.wordprocessingml.document | WordParser | 嵌入图片 | 表格、图表提取 |
| | Excel | .xlsx,.xls | application/vnd.openxmlformats-officedocument.spreadsheetml.sheet | ExcelParser | 否 | 多工作表、公式 |
| | PowerPoint | .pptx,.ppt | application/vnd.openxmlformats-officedocument.presentationml.presentation | PPTParser | 图片内容 | 动画、备注 |
| **文本文档** | 纯文本 | .txt | text/plain | TextParser | 否 | 编码检测 |
| | Markdown | .md,.markdown | text/markdown | MarkdownParser | 否 | 代码块、表格 |
| | RTF | .rtf | application/rtf | RTFParser | 否 | 格式保留 |
| **图片文档** | PNG | .png | image/png | ImageParser | 是 | 透明度处理 |
| | JPEG | .jpg,.jpeg | image/jpeg | ImageParser | 是 | EXIF信息 |
| | TIFF | .tiff,.tif | image/tiff | ImageParser | 是 | 多页支持 |
| | BMP | .bmp | image/bmp | ImageParser | 是 | 大文件处理 |
| **压缩文档** | ZIP | .zip | application/zip | ArchiveParser | 递归 | 嵌套解压 |
| | RAR | .rar | application/x-rar-compressed | ArchiveParser | 递归 | 密码保护 |

### 5.2 格式检测器实现

```java
@Component
@Slf4j
public class DocumentFormatDetector {

    private static final Map<String, DocumentFormat> MIME_TYPE_MAP = Map.of(
        "application/pdf", DocumentFormat.PDF,
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document", DocumentFormat.DOCX,
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", DocumentFormat.XLSX,
        "application/vnd.openxmlformats-officedocument.presentationml.presentation", DocumentFormat.PPTX,
        "text/plain", DocumentFormat.TXT,
        "text/markdown", DocumentFormat.MARKDOWN,
        "image/png", DocumentFormat.PNG,
        "image/jpeg", DocumentFormat.JPEG
    );

    private static final Map<String, DocumentFormat> EXTENSION_MAP = Map.of(
        ".pdf", DocumentFormat.PDF,
        ".docx", DocumentFormat.DOCX,
        ".doc", DocumentFormat.DOC,
        ".xlsx", DocumentFormat.XLSX,
        ".xls", DocumentFormat.XLS,
        ".pptx", DocumentFormat.PPTX,
        ".ppt", DocumentFormat.PPT,
        ".txt", DocumentFormat.TXT,
        ".md", DocumentFormat.MARKDOWN,
        ".png", DocumentFormat.PNG,
        ".jpg", DocumentFormat.JPEG,
        ".jpeg", DocumentFormat.JPEG
    );

    public DocumentFormat detectFormat(DocumentInfo docInfo) {
        try {
            // 1. 基于MIME类型检测
            DocumentFormat formatByMime = detectByMimeType(docInfo.getMimeType());
            if (formatByMime != null) {
                return formatByMime;
            }

            // 2. 基于文件扩展名检测
            DocumentFormat formatByExtension = detectByExtension(docInfo.getFileName());
            if (formatByExtension != null) {
                return formatByExtension;
            }

            // 3. 基于文件头魔数检测
            DocumentFormat formatByMagic = detectByMagicNumber(docInfo.getInputStream());
            if (formatByMagic != null) {
                return formatByMagic;
            }

            throw new UnsupportedFormatException("无法识别的文档格式: " + docInfo.getFileName());

        } catch (Exception e) {
            log.error("文档格式检测失败: {}", docInfo.getFileName(), e);
            throw new DocumentFormatException("格式检测失败", e);
        }
    }

    private DocumentFormat detectByMagicNumber(InputStream inputStream) throws IOException {
        byte[] header = new byte[8];
        inputStream.mark(8);
        int bytesRead = inputStream.read(header);
        inputStream.reset();

        if (bytesRead < 4) return null;

        // PDF魔数: %PDF
        if (header[0] == 0x25 && header[1] == 0x50 && header[2] == 0x44 && header[3] == 0x46) {
            return DocumentFormat.PDF;
        }

        // ZIP/Office魔数: PK
        if (header[0] == 0x50 && header[1] == 0x4B) {
            return detectOfficeFormat(inputStream);
        }

        // PNG魔数
        if (header[0] == (byte)0x89 && header[1] == 0x50 && header[2] == 0x4E && header[3] == 0x47) {
            return DocumentFormat.PNG;
        }

        // JPEG魔数
        if (header[0] == (byte)0xFF && header[1] == (byte)0xD8) {
            return DocumentFormat.JPEG;
        }

        return null;
    }
}
```

## 6. 文档内容提取策略

### 6.1 提取策略工厂

```java
@Component
public class ExtractionStrategyFactory {

    private final Map<DocumentFormat, ExtractionStrategy> strategies;

    public ExtractionStrategyFactory(List<ExtractionStrategy> strategyList) {
        this.strategies = strategyList.stream()
            .collect(Collectors.toMap(
                ExtractionStrategy::getSupportedFormat,
                Function.identity()
            ));
    }

    public ExtractionStrategy getStrategy(DocumentFormat format) {
        ExtractionStrategy strategy = strategies.get(format);
        if (strategy == null) {
            throw new UnsupportedOperationException("不支持的提取策略: " + format);
        }
        return strategy;
    }
}

public interface ExtractionStrategy {
    DocumentFormat getSupportedFormat();
    ExtractionResult extract(DocumentInfo docInfo, ExtractionConfig config);
}
```

### 6.2 PDF提取策略

```java
@Component
@Slf4j
public class PDFExtractionStrategy implements ExtractionStrategy {

    @Autowired
    private OCRService ocrService;

    @Override
    public DocumentFormat getSupportedFormat() {
        return DocumentFormat.PDF;
    }

    @Override
    public ExtractionResult extract(DocumentInfo docInfo, ExtractionConfig config) {
        try (PDDocument document = PDDocument.load(docInfo.getInputStream())) {

            ExtractionResult.Builder resultBuilder = ExtractionResult.builder()
                .documentId(docInfo.getDocumentId());

            for (int pageNum = 1; pageNum <= document.getNumberOfPages(); pageNum++) {
                PageExtractionResult pageResult = extractPage(document, pageNum, config);
                resultBuilder.addPage(pageResult);
            }

            // 提取文档级元数据
            DocumentMetadata metadata = extractDocumentMetadata(document);
            resultBuilder.metadata(metadata);

            return resultBuilder.build();

        } catch (IOException e) {
            throw new ExtractionException("PDF提取失败", e);
        }
    }

    private PageExtractionResult extractPage(PDDocument document, int pageNum, ExtractionConfig config) {
        PageExtractionResult.Builder pageBuilder = PageExtractionResult.builder()
            .pageNumber(pageNum);

        try {
            PDPage page = document.getPage(pageNum - 1);

            // 1. 文本提取
            if (config.isExtractText()) {
                String text = extractTextFromPage(document, pageNum);
                pageBuilder.textContent(text);
            }

            // 2. 图片提取和OCR
            if (config.isExtractImages()) {
                List<ImageContent> images = extractImagesFromPage(page, pageNum, config);
                pageBuilder.images(images);
            }

            // 3. 表格提取
            if (config.isExtractTables()) {
                List<TableContent> tables = extractTablesFromPage(page, pageNum);
                pageBuilder.tables(tables);
            }

            // 4. 页面元数据
            PageMetadata pageMetadata = extractPageMetadata(page, pageNum);
            pageBuilder.metadata(pageMetadata);

            return pageBuilder.build();

        } catch (Exception e) {
            log.error("页面提取失败: 页码{}", pageNum, e);
            return PageExtractionResult.builder()
                .pageNumber(pageNum)
                .error("页面提取失败: " + e.getMessage())
                .build();
        }
    }

    private List<ImageContent> extractImagesFromPage(PDPage page, int pageNum, ExtractionConfig config) {
        List<ImageContent> images = new ArrayList<>();

        try {
            PDResources resources = page.getResources();
            if (resources == null) return images;

            for (COSName name : resources.getXObjectNames()) {
                PDXObject xObject = resources.getXObject(name);

                if (xObject instanceof PDImageXObject) {
                    PDImageXObject image = (PDImageXObject) xObject;
                    BufferedImage bufferedImage = image.getImage();

                    // OCR识别
                    String ocrText = "";
                    if (config.isEnableOCR()) {
                        ocrText = ocrService.recognizeText(bufferedImage);
                    }

                    ImageContent imageContent = ImageContent.builder()
                        .pageNumber(pageNum)
                        .imageIndex(images.size())
                        .width(bufferedImage.getWidth())
                        .height(bufferedImage.getHeight())
                        .ocrText(ocrText)
                        .confidence(calculateOCRConfidence(ocrText))
                        .build();

                    images.add(imageContent);
                }
            }

        } catch (IOException e) {
            log.error("图片提取失败: 页码{}", pageNum, e);
        }

        return images;
    }
}
```

## 7. 文档分割策略选项

### 7.1 分割策略枚举

```java
public enum ChunkingStrategy {
    FIXED_SIZE("固定长度分割", "按固定字符数分割"),
    SEMANTIC("语义分割", "基于语义边界分割"),
    PARAGRAPH("段落分割", "按段落分割"),
    SENTENCE("句子分割", "按句子分割"),
    SECTION("章节分割", "按章节标题分割"),
    SLIDING_WINDOW("滑动窗口", "重叠滑动窗口分割"),
    HYBRID("混合策略", "多种策略组合");

    private final String displayName;
    private final String description;

    ChunkingStrategy(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }
}
```

### 7.2 分割策略配置

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChunkingConfig {

    @Builder.Default
    private ChunkingStrategy strategy = ChunkingStrategy.SEMANTIC;

    // 固定长度分割参数
    @Builder.Default
    private Integer fixedSize = 512;

    @Builder.Default
    private Integer overlapSize = 50;

    // 语义分割参数
    @Builder.Default
    private Double semanticThreshold = 0.7;

    @Builder.Default
    private Integer minChunkSize = 100;

    @Builder.Default
    private Integer maxChunkSize = 1000;

    // 段落分割参数
    @Builder.Default
    private Boolean preserveParagraphs = true;

    @Builder.Default
    private Integer minParagraphLength = 50;

    // 章节分割参数
    private List<String> sectionHeaders = Arrays.asList("第.*章", "第.*节", "\\d+\\.", "\\d+\\.\\d+");

    // 滑动窗口参数
    @Builder.Default
    private Integer windowSize = 300;

    @Builder.Default
    private Integer stepSize = 200;

    // 混合策略权重
    private Map<ChunkingStrategy, Double> strategyWeights = Map.of(
        ChunkingStrategy.SEMANTIC, 0.6,
        ChunkingStrategy.PARAGRAPH, 0.3,
        ChunkingStrategy.FIXED_SIZE, 0.1
    );
}
```

### 7.3 分割策略实现

```java
@Component
@Slf4j
public class SemanticChunkingStrategy implements ChunkingStrategy {

    @Autowired
    private NLPService nlpService;

    @Autowired
    private SentenceSegmenter sentenceSegmenter;

    @Override
    public ChunkingStrategyType getStrategyType() {
        return ChunkingStrategyType.SEMANTIC;
    }

    @Override
    public List<TextChunk> chunk(String text, ChunkingConfig config) {
        List<TextChunk> chunks = new ArrayList<>();

        // 1. 句子分割
        List<String> sentences = sentenceSegmenter.segment(text);

        // 2. 语义相似度计算
        List<SemanticGroup> semanticGroups = groupBySemantic(sentences, config);

        // 3. 生成文本块
        for (int i = 0; i < semanticGroups.size(); i++) {
            SemanticGroup group = semanticGroups.get(i);

            TextChunk chunk = TextChunk.builder()
                .chunkIndex(i)
                .content(group.getCombinedText())
                .startPosition(group.getStartPosition())
                .endPosition(group.getEndPosition())
                .tokenCount(calculateTokenCount(group.getCombinedText()))
                .semanticScore(group.getCoherenceScore())
                .sentences(group.getSentences())
                .build();

            chunks.add(chunk);
        }

        return chunks;
    }

    private List<SemanticGroup> groupBySemantic(List<String> sentences, ChunkingConfig config) {
        List<SemanticGroup> groups = new ArrayList<>();
        SemanticGroup currentGroup = new SemanticGroup();

        for (int i = 0; i < sentences.size(); i++) {
            String sentence = sentences.get(i);

            if (currentGroup.isEmpty()) {
                currentGroup.addSentence(sentence, i);
            } else {
                // 计算语义相似度
                double similarity = nlpService.calculateSimilarity(
                    currentGroup.getCombinedText(), sentence);

                if (similarity >= config.getSemanticThreshold() &&
                    currentGroup.getLength() + sentence.length() <= config.getMaxChunkSize()) {
                    // 添加到当前组
                    currentGroup.addSentence(sentence, i);
                } else {
                    // 开始新组
                    if (currentGroup.getLength() >= config.getMinChunkSize()) {
                        groups.add(currentGroup);
                    }
                    currentGroup = new SemanticGroup();
                    currentGroup.addSentence(sentence, i);
                }
            }
        }

        // 添加最后一组
        if (!currentGroup.isEmpty() && currentGroup.getLength() >= config.getMinChunkSize()) {
            groups.add(currentGroup);
        }

        return groups;
    }
}

@Component
public class FixedSizeChunkingStrategy implements ChunkingStrategy {

    @Override
    public ChunkingStrategyType getStrategyType() {
        return ChunkingStrategyType.FIXED_SIZE;
    }

    @Override
    public List<TextChunk> chunk(String text, ChunkingConfig config) {
        List<TextChunk> chunks = new ArrayList<>();
        int fixedSize = config.getFixedSize();
        int overlapSize = config.getOverlapSize();

        int start = 0;
        int chunkIndex = 0;

        while (start < text.length()) {
            int end = Math.min(start + fixedSize, text.length());

            // 尝试在单词边界分割
            if (end < text.length()) {
                int lastSpace = text.lastIndexOf(' ', end);
                if (lastSpace > start + fixedSize * 0.8) {
                    end = lastSpace;
                }
            }

            String chunkText = text.substring(start, end).trim();

            if (!chunkText.isEmpty()) {
                TextChunk chunk = TextChunk.builder()
                    .chunkIndex(chunkIndex++)
                    .content(chunkText)
                    .startPosition(start)
                    .endPosition(end)
                    .tokenCount(calculateTokenCount(chunkText))
                    .build();

                chunks.add(chunk);
            }

            // 计算下一个起始位置（考虑重叠）
            start = Math.max(start + fixedSize - overlapSize, end);
        }

        return chunks;
    }
}
```

## 8. 智能路由匹配机制

### 8.1 路由决策引擎

```java
@Component
@Slf4j
public class ExtractionRoutingEngine {

    @Autowired
    private DocumentAnalyzer documentAnalyzer;

    @Autowired
    private UserPreferenceService userPreferenceService;

    public RoutingDecision makeRoutingDecision(DocumentInfo docInfo, String userId) {
        try {
            // 1. 文档特征分析
            DocumentFeatures features = documentAnalyzer.analyzeDocument(docInfo);

            // 2. 用户偏好获取
            UserPreferences userPrefs = userPreferenceService.getUserPreferences(userId);

            // 3. 路由决策
            ExtractionStrategy extractionStrategy = selectExtractionStrategy(features, userPrefs);
            ChunkingStrategy chunkingStrategy = selectChunkingStrategy(features, userPrefs);

            // 4. 参数优化
            ExtractionConfig extractionConfig = optimizeExtractionConfig(features, extractionStrategy);
            ChunkingConfig chunkingConfig = optimizeChunkingConfig(features, chunkingStrategy);

            return RoutingDecision.builder()
                .extractionStrategy(extractionStrategy)
                .chunkingStrategy(chunkingStrategy)
                .extractionConfig(extractionConfig)
                .chunkingConfig(chunkingConfig)
                .confidence(calculateConfidence(features))
                .reasoning(generateReasoning(features, extractionStrategy, chunkingStrategy))
                .build();

        } catch (Exception e) {
            log.error("路由决策失败: {}", docInfo.getFileName(), e);
            return getDefaultRoutingDecision();
        }
    }

    private ExtractionStrategy selectExtractionStrategy(DocumentFeatures features, UserPreferences userPrefs) {
        // 基于文档特征的策略选择
        if (features.hasComplexTables() && features.getTableCount() > 5) {
            return ExtractionStrategy.TABLE_FOCUSED;
        }

        if (features.hasImages() && features.getImageTextRatio() > 0.3) {
            return ExtractionStrategy.OCR_ENHANCED;
        }

        if (features.isStructuredDocument() && features.hasClearSections()) {
            return ExtractionStrategy.STRUCTURE_AWARE;
        }

        // 基于用户偏好
        if (userPrefs.preferHighAccuracy()) {
            return ExtractionStrategy.HIGH_ACCURACY;
        }

        if (userPrefs.preferFastProcessing()) {
            return ExtractionStrategy.FAST_PROCESSING;
        }

        return ExtractionStrategy.BALANCED;
    }

    private ChunkingStrategy selectChunkingStrategy(DocumentFeatures features, UserPreferences userPrefs) {
        // 基于文档类型
        if (features.getDocumentType() == DocumentType.ACADEMIC_PAPER) {
            return ChunkingStrategy.SECTION;
        }

        if (features.getDocumentType() == DocumentType.TECHNICAL_MANUAL) {
            return ChunkingStrategy.HYBRID;
        }

        if (features.getDocumentType() == DocumentType.LEGAL_DOCUMENT) {
            return ChunkingStrategy.PARAGRAPH;
        }

        // 基于文档结构
        if (features.hasStrongStructure()) {
            return ChunkingStrategy.SEMANTIC;
        }

        if (features.isPlainText()) {
            return ChunkingStrategy.FIXED_SIZE;
        }

        return ChunkingStrategy.SEMANTIC;
    }
}

@Data
@Builder
public class DocumentFeatures {
    private DocumentType documentType;
    private long fileSize;
    private int pageCount;
    private int paragraphCount;
    private int tableCount;
    private int imageCount;
    private double imageTextRatio;
    private boolean hasComplexTables;
    private boolean hasImages;
    private boolean isStructuredDocument;
    private boolean hasClearSections;
    private boolean hasStrongStructure;
    private boolean isPlainText;
    private String language;
    private double textDensity;
    private List<String> detectedSections;
    private Map<String, Object> additionalFeatures;
}
```

## 9. 分段元数据统一规范

### 9.1 元数据字段定义

| 字段名称 | 数据类型 | 是否必填 | 默认值 | 说明 |
|----------|----------|----------|--------|------|
| **基础信息** |
| `chunk_id` | String | 是 | UUID | 分段唯一标识符 |
| `document_id` | String | 是 | - | 所属文档ID |
| `chunk_index` | Integer | 是 | - | 分段在文档中的序号（从0开始） |
| `content` | String | 是 | - | 分段文本内容 |
| `content_hash` | String | 是 | - | 内容SHA256哈希值 |
| **位置信息** |
| `page_number` | Integer | 否 | null | 所在页码（从1开始） |
| `start_position` | Integer | 否 | null | 在原文档中的起始字符位置 |
| `end_position` | Integer | 否 | null | 在原文档中的结束字符位置 |
| `paragraph_index` | Integer | 否 | null | 所在段落索引 |
| `section_title` | String | 否 | null | 所属章节标题 |
| `section_level` | Integer | 否 | null | 章节层级（1-6） |
| **内容特征** |
| `token_count` | Integer | 是 | 0 | Token数量 |
| `character_count` | Integer | 是 | 0 | 字符数量 |
| `word_count` | Integer | 是 | 0 | 单词数量 |
| `sentence_count` | Integer | 是 | 0 | 句子数量 |
| `language` | String | 否 | "zh" | 主要语言代码 |
| **质量指标** |
| `extraction_confidence` | Double | 是 | 0.0 | 提取置信度（0-1） |
| `ocr_confidence` | Double | 否 | null | OCR识别置信度（0-1） |
| `semantic_coherence` | Double | 否 | null | 语义连贯性分数（0-1） |
| `readability_score` | Double | 否 | null | 可读性分数 |
| **处理信息** |
| `extraction_strategy` | String | 是 | - | 使用的提取策略 |
| `chunking_strategy` | String | 是 | - | 使用的分割策略 |
| `processing_time` | Long | 是 | 0 | 处理耗时（毫秒） |
| `created_time` | DateTime | 是 | now() | 创建时间 |
| `updated_time` | DateTime | 是 | now() | 更新时间 |
| **向量信息** |
| `vector_id` | String | 否 | null | 对应向量ID |
| `embedding_model` | String | 否 | null | 使用的Embedding模型 |
| `vector_dimension` | Integer | 否 | null | 向量维度 |
| **扩展信息** |
| `tags` | List<String> | 否 | [] | 标签列表 |
| `categories` | List<String> | 否 | [] | 分类列表 |
| `keywords` | List<String> | 否 | [] | 关键词列表 |
| `entities` | List<Entity> | 否 | [] | 命名实体列表 |
| `custom_metadata` | Map<String,Object> | 否 | {} | 自定义元数据 |

### 9.2 元数据实体类

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("rag_chunk_metadata")
public class ChunkMetadata {

    @TableId(type = IdType.ASSIGN_ID)
    private String chunkId;

    @TableField("document_id")
    private String documentId;

    @TableField("chunk_index")
    private Integer chunkIndex;

    @TableField("content_hash")
    private String contentHash;

    // 位置信息
    @TableField("page_number")
    private Integer pageNumber;

    @TableField("start_position")
    private Integer startPosition;

    @TableField("end_position")
    private Integer endPosition;

    @TableField("paragraph_index")
    private Integer paragraphIndex;

    @TableField("section_title")
    private String sectionTitle;

    @TableField("section_level")
    private Integer sectionLevel;

    // 内容特征
    @TableField("token_count")
    private Integer tokenCount;

    @TableField("character_count")
    private Integer characterCount;

    @TableField("word_count")
    private Integer wordCount;

    @TableField("sentence_count")
    private Integer sentenceCount;

    @TableField("language")
    private String language;

    // 质量指标
    @TableField("extraction_confidence")
    private Double extractionConfidence;

    @TableField("ocr_confidence")
    private Double ocrConfidence;

    @TableField("semantic_coherence")
    private Double semanticCoherence;

    @TableField("readability_score")
    private Double readabilityScore;

    // 处理信息
    @TableField("extraction_strategy")
    private String extractionStrategy;

    @TableField("chunking_strategy")
    private String chunkingStrategy;

    @TableField("processing_time")
    private Long processingTime;

    @TableField("created_time")
    private LocalDateTime createdTime;

    @TableField("updated_time")
    private LocalDateTime updatedTime;

    // 向量信息
    @TableField("vector_id")
    private String vectorId;

    @TableField("embedding_model")
    private String embeddingModel;

    @TableField("vector_dimension")
    private Integer vectorDimension;

    // 扩展信息（JSON存储）
    @TableField(value = "tags", typeHandler = JacksonTypeHandler.class)
    private List<String> tags;

    @TableField(value = "categories", typeHandler = JacksonTypeHandler.class)
    private List<String> categories;

    @TableField(value = "keywords", typeHandler = JacksonTypeHandler.class)
    private List<String> keywords;

    @TableField(value = "entities", typeHandler = JacksonTypeHandler.class)
    private List<NamedEntity> entities;

    @TableField(value = "custom_metadata", typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> customMetadata;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NamedEntity {
    private String text;
    private String type;
    private Integer startOffset;
    private Integer endOffset;
    private Double confidence;
    private Map<String, Object> attributes;
}
```

## 10. 文档摘要提取算法（基于大模型）

### 10.1 LLM摘要提取器

```java
@Component
@Slf4j
public class LLMSummaryExtractor {

    @Autowired
    private LLMModelRouter llmRouter;

    @Autowired
    private SummaryPromptBuilder promptBuilder;

    @Autowired
    private SummaryCacheService summaryCache;

    public SummaryResult extractSummary(String content, SummaryConfig config) {
        try {
            // 1. 检查缓存
            String cacheKey = generateSummaryCacheKey(content, config);
            SummaryResult cachedResult = summaryCache.getCachedSummary(cacheKey);
            if (cachedResult != null) {
                log.debug("命中摘要缓存: {}", cacheKey);
                return cachedResult;
            }

            // 2. 内容预处理和分段
            List<String> contentChunks = preprocessContent(content, config);

            // 3. 根据内容长度选择策略
            SummaryResult result;
            if (contentChunks.size() == 1) {
                result = singleChunkSummary(contentChunks.get(0), config);
            } else {
                result = multiChunkSummary(contentChunks, config);
            }

            // 4. 缓存结果
            if (result.isSuccess()) {
                summaryCache.cacheSummary(cacheKey, result);
            }

            return result;

        } catch (Exception e) {
            log.error("LLM摘要提取失败", e);
            return SummaryResult.failure("摘要提取失败: " + e.getMessage());
        }
    }

    private SummaryResult singleChunkSummary(String content, SummaryConfig config) {
        // 构建提示词
        String prompt = promptBuilder.buildSummaryPrompt(content, config);

        // 调用LLM
        LLMModel model = llmRouter.selectModel(config.getModelPreference());
        LLMRequest request = LLMRequest.builder()
            .prompt(prompt)
            .maxTokens(config.getMaxTokens())
            .temperature(config.getTemperature())
            .build();

        LLMResponse response = model.generate(request);

        if (response.isSuccess()) {
            return SummaryResult.builder()
                .summary(response.getContent())
                .strategy(SummaryStrategy.LLM_SINGLE)
                .confidence(response.getConfidence())
                .modelUsed(model.getModelName())
                .tokenUsage(response.getTokenUsage())
                .build();
        } else {
            throw new SummaryException("LLM摘要生成失败: " + response.getErrorMessage());
        }
    }

    private SummaryResult multiChunkSummary(List<String> chunks, SummaryConfig config) {
        List<String> chunkSummaries = new ArrayList<>();

        // 1. 对每个分块生成摘要
        for (int i = 0; i < chunks.size(); i++) {
            String chunk = chunks.get(i);

            SummaryConfig chunkConfig = config.toBuilder()
                .maxTokens(config.getMaxTokens() / chunks.size())
                .summaryType(SummaryType.CHUNK)
                .build();

            SummaryResult chunkResult = singleChunkSummary(chunk, chunkConfig);
            if (chunkResult.isSuccess()) {
                chunkSummaries.add(chunkResult.getSummary());
            }
        }

        // 2. 合并分块摘要
        String combinedSummary = String.join("\n", chunkSummaries);

        // 3. 生成最终摘要
        SummaryConfig finalConfig = config.toBuilder()
            .summaryType(SummaryType.FINAL)
            .build();

        return singleChunkSummary(combinedSummary, finalConfig);
    }

    private List<String> preprocessContent(String content, SummaryConfig config) {
        int maxChunkSize = config.getMaxInputTokens() * 4; // 粗略估算

        if (content.length() <= maxChunkSize) {
            return Collections.singletonList(content);
        }

        // 智能分块，保持段落完整性
        List<String> chunks = new ArrayList<>();
        String[] paragraphs = content.split("\n\n");

        StringBuilder currentChunk = new StringBuilder();
        for (String paragraph : paragraphs) {
            if (currentChunk.length() + paragraph.length() > maxChunkSize) {
                if (currentChunk.length() > 0) {
                    chunks.add(currentChunk.toString().trim());
                    currentChunk = new StringBuilder();
                }
            }
            currentChunk.append(paragraph).append("\n\n");
        }

        if (currentChunk.length() > 0) {
            chunks.add(currentChunk.toString().trim());
        }

        return chunks;
    }
}

### 10.2 摘要提示词构建器

```java
@Component
public class SummaryPromptBuilder {

    public String buildSummaryPrompt(String content, SummaryConfig config) {
        StringBuilder prompt = new StringBuilder();

        // 1. 系统角色设定
        prompt.append("你是一个专业的文档摘要专家。请根据以下要求为给定的文档内容生成摘要。\n\n");

        // 2. 摘要要求
        prompt.append("摘要要求：\n");
        prompt.append("- 长度：").append(config.getMaxLength()).append("字以内\n");
        prompt.append("- 语言：").append(config.getLanguage()).append("\n");
        prompt.append("- 风格：").append(getSummaryStyle(config.getSummaryType())).append("\n");

        if (config.getKeyPoints() != null && !config.getKeyPoints().isEmpty()) {
            prompt.append("- 重点关注：").append(String.join("、", config.getKeyPoints())).append("\n");
        }

        prompt.append("\n");

        // 3. 摘要类型特定指令
        switch (config.getSummaryType()) {
            case EXECUTIVE:
                prompt.append("请生成执行摘要，重点突出关键决策点、主要结论和行动建议。\n");
                break;
            case TECHNICAL:
                prompt.append("请生成技术摘要，重点突出技术要点、方法论和实现细节。\n");
                break;
            case ACADEMIC:
                prompt.append("请生成学术摘要，重点突出研究目的、方法、主要发现和结论。\n");
                break;
            case GENERAL:
            default:
                prompt.append("请生成通用摘要，涵盖文档的主要内容和核心观点。\n");
                break;
        }

        prompt.append("\n");

        // 4. 输出格式要求
        prompt.append("输出格式：\n");
        prompt.append("- 使用简洁明了的语言\n");
        prompt.append("- 保持逻辑结构清晰\n");
        prompt.append("- 避免重复和冗余\n");
        prompt.append("- 确保信息准确性\n\n");

        // 5. 文档内容
        prompt.append("文档内容：\n");
        prompt.append("```\n");
        prompt.append(content);
        prompt.append("\n```\n\n");

        // 6. 生成指令
        prompt.append("请基于以上文档内容生成摘要：");

        return prompt.toString();
    }

    private String getSummaryStyle(SummaryType type) {
        switch (type) {
            case EXECUTIVE:
                return "商务正式";
            case TECHNICAL:
                return "技术专业";
            case ACADEMIC:
                return "学术严谨";
            case GENERAL:
            default:
                return "通俗易懂";
        }
    }
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SummaryConfig {

    @Builder.Default
    private SummaryStrategy strategy = SummaryStrategy.LLM_SINGLE;

    @Builder.Default
    private SummaryType summaryType = SummaryType.GENERAL;

    @Builder.Default
    private String language = "中文";

    @Builder.Default
    private Integer maxLength = 200;

    @Builder.Default
    private Integer maxTokens = 500;

    @Builder.Default
    private Integer maxInputTokens = 4000;

    @Builder.Default
    private Double temperature = 0.3;

    @Builder.Default
    private String modelPreference = "gpt-4";

    private List<String> keyPoints;

    private Map<String, Object> customInstructions;
}

public enum SummaryType {
    GENERAL("通用摘要"),
    EXECUTIVE("执行摘要"),
    TECHNICAL("技术摘要"),
    ACADEMIC("学术摘要"),
    CHUNK("分块摘要"),
    FINAL("最终摘要");

    private final String description;

    SummaryType(String description) {
        this.description = description;
    }
}

public enum SummaryStrategy {
    LLM_SINGLE("单次LLM生成"),
    LLM_MULTI_CHUNK("多分块LLM生成"),
    LLM_ITERATIVE("迭代式LLM生成");

    private final String description;

    SummaryStrategy(String description) {
        this.description = description;
    }
}
```

## 11. 关键词提取算法（基于大模型）

### 11.1 LLM关键词提取器

```java
@Component
@Slf4j
public class LLMKeywordExtractor {

    @Autowired
    private LLMModelRouter llmRouter;

    @Autowired
    private KeywordPromptBuilder promptBuilder;

    @Autowired
    private KeywordCacheService keywordCache;

    @Autowired
    private NERExtractor nerExtractor;

    public KeywordExtractionResult extractKeywords(String content, KeywordConfig config) {
        try {
            // 1. 检查缓存
            String cacheKey = generateKeywordCacheKey(content, config);
            KeywordExtractionResult cachedResult = keywordCache.getCachedKeywords(cacheKey);
            if (cachedResult != null) {
                log.debug("命中关键词缓存: {}", cacheKey);
                return cachedResult;
            }

            List<Keyword> allKeywords = new ArrayList<>();

            // 2. LLM关键词提取
            List<Keyword> llmKeywords = extractKeywordsWithLLM(content, config);
            allKeywords.addAll(llmKeywords);

            // 3. 命名实体识别（作为补充）
            if (config.isEnableNER()) {
                List<Keyword> nerKeywords = nerExtractor.extract(content, config);
                allKeywords.addAll(nerKeywords);
            }

            // 4. 关键词融合和去重
            List<Keyword> finalKeywords = fuseAndDeduplicate(allKeywords, config);

            KeywordExtractionResult result = KeywordExtractionResult.builder()
                .keywords(finalKeywords)
                .totalCount(finalKeywords.size())
                .confidence(calculateOverallConfidence(finalKeywords))
                .extractionMethods(Arrays.asList("LLM", config.isEnableNER() ? "NER" : null))
                .build();

            // 5. 缓存结果
            if (result.isSuccess()) {
                keywordCache.cacheKeywords(cacheKey, result);
            }

            return result;

        } catch (Exception e) {
            log.error("LLM关键词提取失败", e);
            return KeywordExtractionResult.failure("关键词提取失败: " + e.getMessage());
        }
    }

    private List<Keyword> extractKeywordsWithLLM(String content, KeywordConfig config) {
        // 构建提示词
        String prompt = promptBuilder.buildKeywordPrompt(content, config);

        // 调用LLM
        LLMModel model = llmRouter.selectModel(config.getModelPreference());
        LLMRequest request = LLMRequest.builder()
            .prompt(prompt)
            .maxTokens(config.getMaxTokens())
            .temperature(config.getTemperature())
            .build();

        LLMResponse response = model.generate(request);

        if (response.isSuccess()) {
            // 解析LLM返回的关键词
            return parseKeywordsFromLLMResponse(response.getContent(), config);
        } else {
            throw new KeywordExtractionException("LLM关键词提取失败: " + response.getErrorMessage());
        }
    }

    private List<Keyword> parseKeywordsFromLLMResponse(String llmResponse, KeywordConfig config) {
        List<Keyword> keywords = new ArrayList<>();

        try {
            // 解析JSON格式的关键词响应
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(llmResponse);

            if (rootNode.has("keywords") && rootNode.get("keywords").isArray()) {
                ArrayNode keywordsArray = (ArrayNode) rootNode.get("keywords");

                for (JsonNode keywordNode : keywordsArray) {
                    String text = keywordNode.get("text").asText();
                    double score = keywordNode.has("score") ? keywordNode.get("score").asDouble() : 0.8;
                    String type = keywordNode.has("type") ? keywordNode.get("type").asText() : "GENERAL";
                    String explanation = keywordNode.has("explanation") ? keywordNode.get("explanation").asText() : "";

                    Keyword keyword = Keyword.builder()
                        .text(text)
                        .score(score)
                        .source("LLM")
                        .type(type)
                        .attributes(Map.of("explanation", explanation))
                        .build();

                    keywords.add(keyword);
                }
            }

        } catch (Exception e) {
            log.warn("解析LLM关键词响应失败，尝试简单文本解析: {}", e.getMessage());
            // 降级到简单文本解析
            keywords = parseKeywordsFromPlainText(llmResponse);
        }

        return keywords;
    }

    private List<Keyword> parseKeywordsFromPlainText(String text) {
        List<Keyword> keywords = new ArrayList<>();

        // 简单的文本解析，查找关键词模式
        String[] lines = text.split("\n");
        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) continue;

            // 移除序号和特殊字符
            line = line.replaceAll("^\\d+[.、]\\s*", "")
                      .replaceAll("^[•\\-*]\\s*", "")
                      .trim();

            if (line.length() > 1 && line.length() < 50) {
                Keyword keyword = Keyword.builder()
                    .text(line)
                    .score(0.7) // 默认分数
                    .source("LLM")
                    .type("GENERAL")
                    .build();

                keywords.add(keyword);
            }
        }

        return keywords;
    }

    private List<Keyword> fuseAndDeduplicate(List<Keyword> keywords, KeywordConfig config) {
        // 1. 按词汇分组（忽略大小写）
        Map<String, List<Keyword>> groupedKeywords = keywords.stream()
            .collect(Collectors.groupingBy(k -> k.getText().toLowerCase()));

        // 2. 融合同一词汇的不同来源分数
        List<Keyword> fusedKeywords = new ArrayList<>();

        for (Map.Entry<String, List<Keyword>> entry : groupedKeywords.entrySet()) {
            List<Keyword> group = entry.getValue();

            if (group.size() == 1) {
                fusedKeywords.add(group.get(0));
            } else {
                // 多源融合，LLM结果优先
                Keyword fusedKeyword = fuseKeywords(group, config);
                fusedKeywords.add(fusedKeyword);
            }
        }

        // 3. 排序和截取
        return fusedKeywords.stream()
            .filter(k -> k.getScore() >= config.getMinScore())
            .sorted((k1, k2) -> Double.compare(k2.getScore(), k1.getScore()))
            .limit(config.getMaxKeywords())
            .collect(Collectors.toList());
    }

    private Keyword fuseKeywords(List<Keyword> keywords, KeywordConfig config) {
        // LLM结果优先，如果有LLM结果就使用LLM的
        Optional<Keyword> llmKeyword = keywords.stream()
            .filter(k -> "LLM".equals(k.getSource()))
            .findFirst();

        if (llmKeyword.isPresent()) {
            Keyword base = llmKeyword.get();

            // 如果还有其他来源，稍微提升分数
            if (keywords.size() > 1) {
                double boostedScore = Math.min(base.getScore() * 1.1, 1.0);
                return base.toBuilder()
                    .score(boostedScore)
                    .sources(keywords.stream().map(Keyword::getSource).collect(Collectors.toList()))
                    .build();
            }

            return base;
        }

        // 如果没有LLM结果，使用传统融合方法
        String text = keywords.get(0).getText();
        double avgScore = keywords.stream().mapToDouble(Keyword::getScore).average().orElse(0.0);

        return Keyword.builder()
            .text(text)
            .score(avgScore)
            .source("FUSED")
            .sources(keywords.stream().map(Keyword::getSource).collect(Collectors.toList()))
            .build();
    }
}

### 11.2 关键词提示词构建器

```java
@Component
public class KeywordPromptBuilder {

    public String buildKeywordPrompt(String content, KeywordConfig config) {
        StringBuilder prompt = new StringBuilder();

        // 1. 系统角色设定
        prompt.append("你是一个专业的关键词提取专家。请从给定的文档内容中提取最重要的关键词。\n\n");

        // 2. 提取要求
        prompt.append("提取要求：\n");
        prompt.append("- 数量：提取").append(config.getMaxKeywords()).append("个最重要的关键词\n");
        prompt.append("- 类型：包括核心概念、专业术语、重要实体、关键主题\n");
        prompt.append("- 语言：").append(config.getLanguage()).append("\n");
        prompt.append("- 长度：单个关键词1-8个字符\n");

        if (config.getKeywordTypes() != null && !config.getKeywordTypes().isEmpty()) {
            prompt.append("- 重点类型：").append(String.join("、", config.getKeywordTypes())).append("\n");
        }

        prompt.append("\n");

        // 3. 输出格式要求
        prompt.append("输出格式：请以JSON格式返回，包含以下字段：\n");
        prompt.append("```json\n");
        prompt.append("{\n");
        prompt.append("  \"keywords\": [\n");
        prompt.append("    {\n");
        prompt.append("      \"text\": \"关键词文本\",\n");
        prompt.append("      \"score\": 0.95,\n");
        prompt.append("      \"type\": \"CONCEPT|ENTITY|TERM|TOPIC\",\n");
        prompt.append("      \"explanation\": \"选择此关键词的原因\"\n");
        prompt.append("    }\n");
        prompt.append("  ]\n");
        prompt.append("}\n");
        prompt.append("```\n\n");

        // 4. 评分标准
        prompt.append("评分标准（0-1分）：\n");
        prompt.append("- 0.9-1.0：核心主题词，文档最重要的概念\n");
        prompt.append("- 0.8-0.9：重要概念词，频繁出现且有重要意义\n");
        prompt.append("- 0.7-0.8：相关术语，专业词汇或重要实体\n");
        prompt.append("- 0.6-0.7：辅助词汇，有一定重要性但非核心\n\n");

        // 5. 文档内容
        prompt.append("文档内容：\n");
        prompt.append("```\n");
        prompt.append(content);
        prompt.append("\n```\n\n");

        // 6. 生成指令
        prompt.append("请分析以上文档内容，提取最重要的关键词并按重要性排序：");

        return prompt.toString();
    }
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KeywordConfig {

    @Builder.Default
    private Integer maxKeywords = 10;

    @Builder.Default
    private Double minScore = 0.6;

    @Builder.Default
    private String language = "中文";

    @Builder.Default
    private Integer maxTokens = 800;

    @Builder.Default
    private Double temperature = 0.1;

    @Builder.Default
    private String modelPreference = "gpt-4";

    @Builder.Default
    private Boolean enableNER = true;

    @Builder.Default
    private Integer minWordLength = 2;

    private List<String> keywordTypes;

    private Map<String, Object> customInstructions;
}

public enum KeywordType {
    CONCEPT("概念词"),
    ENTITY("实体词"),
    TERM("术语词"),
    TOPIC("主题词"),
    ACTION("动作词"),
    ATTRIBUTE("属性词");

    private final String description;

    KeywordType(String description) {
        this.description = description;
    }
}

### 11.3 关键词缓存服务

```java
@Service
public class KeywordCacheService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String KEYWORD_CACHE_PREFIX = "keyword:cache:";
    private static final Duration KEYWORD_CACHE_TTL = Duration.ofDays(3);

    public KeywordExtractionResult getCachedKeywords(String cacheKey) {
        try {
            String key = KEYWORD_CACHE_PREFIX + cacheKey;
            return (KeywordExtractionResult) redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            log.warn("获取关键词缓存失败: {}", cacheKey, e);
            return null;
        }
    }

    public void cacheKeywords(String cacheKey, KeywordExtractionResult result) {
        try {
            String key = KEYWORD_CACHE_PREFIX + cacheKey;
            redisTemplate.opsForValue().set(key, result, KEYWORD_CACHE_TTL);
        } catch (Exception e) {
            log.warn("存储关键词缓存失败: {}", cacheKey, e);
        }
    }

    public String generateCacheKey(String content, KeywordConfig config) {
        String contentHash = DigestUtils.sha256Hex(content);
        String configHash = DigestUtils.sha256Hex(JsonUtils.toJson(config));
        return contentHash + ":" + configHash;
    }
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Keyword {
    private String text;
    private Double score;
    private String source;
    private List<String> sources;
    private Integer frequency;
    private String type;
    private Integer startOffset;
    private Integer endOffset;
    private Map<String, Object> attributes;

    public Keyword toBuilder() {
        return Keyword.builder()
            .text(this.text)
            .score(this.score)
            .source(this.source)
            .sources(this.sources)
            .frequency(this.frequency)
            .type(this.type)
            .startOffset(this.startOffset)
            .endOffset(this.endOffset)
            .attributes(this.attributes);
    }
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KeywordExtractionResult {
    private List<Keyword> keywords;
    private Integer totalCount;
    private Double confidence;
    private List<String> extractionMethods;
    private String errorMessage;
    private Boolean success;

    public static KeywordExtractionResult failure(String errorMessage) {
        return KeywordExtractionResult.builder()
            .success(false)
            .errorMessage(errorMessage)
            .keywords(Collections.emptyList())
            .totalCount(0)
            .confidence(0.0)
            .build();
    }

    public boolean isSuccess() {
        return Boolean.TRUE.equals(success);
    }
}
```

## 12. 性能优化策略

### 12.1 并发处理优化

```java
@Configuration
public class ParseTaskExecutorConfig {

    @Bean("parseTaskExecutor")
    public ThreadPoolTaskExecutor parseTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(50);
        executor.setQueueCapacity(200);
        executor.setThreadNamePrefix("parse-task-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    @Bean("chunkingTaskExecutor")
    public ThreadPoolTaskExecutor chunkingTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(8);
        executor.setMaxPoolSize(32);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("chunk-task-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}
```

### 12.2 缓存策略优化

```java
@Service
public class ParseCacheService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String PARSE_CACHE_PREFIX = "parse:cache:";
    private static final Duration CACHE_TTL = Duration.ofHours(24);

    @Cacheable(value = "parseResult", key = "#fileHash")
    public ParseResult getCachedResult(String fileHash) {
        String key = PARSE_CACHE_PREFIX + fileHash;
        return (ParseResult) redisTemplate.opsForValue().get(key);
    }

    @CachePut(value = "parseResult", key = "#fileHash")
    public ParseResult cacheResult(String fileHash, ParseResult result) {
        String key = PARSE_CACHE_PREFIX + fileHash;
        redisTemplate.opsForValue().set(key, result, CACHE_TTL);
        return result;
    }
}
```

## 13. 错误处理与重试

### 13.1 重试机制

```java
@Component
public class ParseRetryHandler {

    @Retryable(
        value = {DocumentParseException.class},
        maxAttempts = 3,
        backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public ParseResult parseWithRetry(DocumentInfo docInfo) {
        return documentParseManager.parseDocument(docInfo);
    }

    @Recover
    public ParseResult recover(DocumentParseException ex, DocumentInfo docInfo) {
        log.error("文档解析最终失败，已达最大重试次数: {}", docInfo.getFileName(), ex);
        return ParseResult.failure("解析失败，已达最大重试次数: " + ex.getMessage());
    }
}
```

---

**文档解析与预处理模块设计已完善，包含了详细的技术实现细节。**
```
